name: Setup Python and SCons
description: Setup Python, install the pip version of SCons.

inputs:
  python-version:
    description: The Python version to use.
    default: 3.x
  python-arch:
    description: The Python architecture.
    default: x64
  scons-version:
    description: The SCons version to use.
    default: 4.9.0

runs:
  using: composite
  steps:
    - name: Set up Python 3.x
      uses: actions/setup-python@v5
      with:
        # Semantic version range syntax or exact version of a Python version.
        python-version: ${{ inputs.python-version }}
        # Optional - x64 or x86 architecture, defaults to x64.
        architecture: ${{ inputs.python-arch }}

    - name: Setup SCons
      shell: bash
      run: |
        python -c "import sys; print(sys.version)"
        python -m pip install scons==${{ inputs.scons-version }}
        scons --version

    - name: Setup problem matchers
      shell: bash
      run: echo ::add-matcher::misc/utility/problem-matchers.json
