name: 🍏 iOS Builds
on:
  workflow_call:

# Global Settings
env:
  SCONS_FLAGS: >-
    dev_mode=yes
    module_text_server_fb_enabled=yes
    tests=no
    debug_symbols=no

jobs:
  ios-template:
    runs-on: macos-latest
    name: Template (target=template_release)
    timeout-minutes: 60

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Restore Godot build cache
        uses: ./.github/actions/godot-cache-restore
        continue-on-error: true

      - name: Setup Python and SCons
        uses: ./.github/actions/godot-deps

      - name: Compilation (arm64)
        uses: ./.github/actions/godot-build
        with:
          scons-flags: ${{ env.SCONS_FLAGS }}
          platform: ios
          target: template_release

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        continue-on-error: true

      - name: Upload artifact
        uses: ./.github/actions/upload-artifact
