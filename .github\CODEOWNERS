# Lines starting with '#' are comments.
# Each line is a file pattern followed by one or more owners.
# Owners can be @users, @org/teams or emails.

# Core

/core/                                        @godotengine/core
/core/crypto/                                 @godotengine/network
/core/debugger/                               @godotengine/debugger
/core/extension/                              @godotengine/gdextension
/core/input/                                  @godotengine/input

# Doc

/doc/                                         @godotengine/documentation

# Drivers

## Audio
/drivers/alsa/                                @godotengine/audio
/drivers/alsamidi/                            @godotengine/audio
/drivers/coreaudio/                           @godotengine/audio
/drivers/coremidi/                            @godotengine/audio
/drivers/pulseaudio/                          @godotengine/audio
/drivers/wasapi/                              @godotengine/audio
/drivers/winmidi/                             @godotengine/audio
/drivers/xaudio2/                             @godotengine/audio

## Rendering
/drivers/d3d12/                               @godotengine/rendering
/drivers/dummy/                               @godotengine/rendering
/drivers/egl/                                 @godotengine/rendering
/drivers/gles3/                               @godotengine/rendering
/drivers/metal/                               @godotengine/rendering
/drivers/spirv-reflect/                       @godotengine/rendering
/drivers/vulkan/                              @godotengine/rendering

## OS
/drivers/unix/                                @godotengine/linux-bsd
/drivers/windows/                             @godotengine/windows

## Misc
/drivers/png/                                 @godotengine/import

# Editor

/editor/                                      @godotengine/docks
/editor/script/                               @godotengine/script-editor
/editor/shader/                               @godotengine/script-editor @godotengine/shaders
/editor/animation/                            @godotengine/animation
/editor/audio/                                @godotengine/audio
/editor/debugger/                             @godotengine/debugger
/editor/doc/                                  @godotengine/documentation
/editor/docks/                                @godotengine/docks
/editor/gui/                                  @godotengine/usability @godotengine/gui-nodes
/editor/icons/                                @godotengine/usability
/editor/import/                               @godotengine/import
/editor/inspector/                            @godotengine/docks
/editor/scene/2d/                             @godotengine/2d-editor
/editor/scene/2d/physics                      @godotengine/2d-editor @godotengine/physics
/editor/scene/3d/                             @godotengine/3d-editor
/editor/scene/3d/physics                      @godotengine/3d-editor @godotengine/physics
/editor/scene/gui/                            @godotengine/gui-nodes
/editor/themes/                               @godotengine/usability @godotengine/gui-nodes
/editor/translations/                         @godotengine/usability

# Main

/main/                                        @godotengine/core

# Misc

/misc/                                        @godotengine/buildsystem
/misc/extension_api_validation/               @godotengine/gdextension @godotengine/dotnet

# Modules

## Audio (+ video)
/modules/interactive_music/                   @godotengine/audio
/modules/interactive_music/doc_classes/       @godotengine/audio @godotengine/documentation
/modules/minimp3/                             @godotengine/audio
/modules/minimp3/doc_classes/                 @godotengine/audio @godotengine/documentation
/modules/ogg/                                 @godotengine/audio
/modules/ogg/doc_classes/                     @godotengine/audio @godotengine/documentation
/modules/theora/                              @godotengine/audio
/modules/theora/doc_classes/                  @godotengine/audio @godotengine/documentation
/modules/vorbis/                              @godotengine/audio
/modules/vorbis/doc_classes/                  @godotengine/audio @godotengine/documentation

## Import
/modules/astcenc/                             @godotengine/import
/modules/basis_universal/                     @godotengine/import
/modules/bcdec/                               @godotengine/import
/modules/betsy/                               @godotengine/import
/modules/bmp/                                 @godotengine/import
/modules/cvtt/                                @godotengine/import
/modules/dds/                                 @godotengine/import
/modules/etcpak/                              @godotengine/import
/modules/fbx/                                 @godotengine/import
/modules/fbx/doc_classes/                     @godotengine/import @godotengine/documentation
/modules/gltf/                                @godotengine/import
/modules/gltf/doc_classes/                    @godotengine/import @godotengine/documentation
/modules/gltf/tests/                          @godotengine/import @godotengine/tests
/modules/hdr/                                 @godotengine/import
/modules/jpg/                                 @godotengine/import
/modules/ktx/                                 @godotengine/import
/modules/squish/                              @godotengine/import
/modules/svg/                                 @godotengine/import
/modules/tga/                                 @godotengine/import
/modules/tinyexr/                             @godotengine/import
/modules/webp/                                @godotengine/import

## Network
/modules/enet/                                @godotengine/network
/modules/enet/doc_classes/                    @godotengine/network @godotengine/documentation
/modules/mbedtls/                             @godotengine/network
/modules/mbedtls/tests/                       @godotengine/network @godotengine/tests
/modules/multiplayer/                         @godotengine/network
/modules/multiplayer/doc_classes/             @godotengine/network @godotengine/documentation
/modules/multiplayer/tests/                   @godotengine/network @godotengine/tests
/modules/upnp/                                @godotengine/network
/modules/upnp/doc_classes/                    @godotengine/network @godotengine/documentation
/modules/webrtc/                              @godotengine/network
/modules/webrtc/doc_classes/                  @godotengine/network @godotengine/documentation
/modules/websocket/                           @godotengine/network
/modules/websocket/doc_classes/               @godotengine/network @godotengine/documentation

## Physics
/modules/godot_physics_2d/                    @godotengine/physics
/modules/godot_physics_3d/                    @godotengine/physics
/modules/jolt_physics/                        @godotengine/physics

## Rendering
/modules/glslang/                             @godotengine/rendering
/modules/lightmapper_rd/                      @godotengine/rendering
/modules/meshoptimizer/                       @godotengine/rendering
/modules/raycast/                             @godotengine/rendering
/modules/vhacd/                               @godotengine/rendering
/modules/xatlas_unwrap/                       @godotengine/rendering

## Scripting
/modules/gdscript/                            @godotengine/gdscript
/modules/gdscript/doc_classes/                @godotengine/gdscript @godotengine/documentation
/modules/gdscript/icons/                      @godotengine/gdscript @godotengine/usability
/modules/gdscript/tests/                      @godotengine/gdscript @godotengine/tests
/modules/jsonrpc/                             @godotengine/gdscript @godotengine/network
/modules/jsonrpc/tests/                       @godotengine/gdscript @godotengine/network @godotengine/tests
/modules/mono/                                @godotengine/dotnet
/modules/mono/doc_classes/                    @godotengine/dotnet @godotengine/documentation
/modules/mono/icons/                          @godotengine/dotnet @godotengine/usability

## Text
/modules/freetype/                            @godotengine/buildsystem
/modules/msdfgen/                             @godotengine/buildsystem
/modules/text_server_adv/                     @godotengine/gui-nodes
/modules/text_server_adv/doc_classes/         @godotengine/gui-nodes @godotengine/documentation
/modules/text_server_fb/                      @godotengine/gui-nodes
/modules/text_server_fb/doc_classes/          @godotengine/gui-nodes @godotengine/documentation

## XR
/modules/camera/                              @godotengine/xr
/modules/mobile_vr/                           @godotengine/xr
/modules/mobile_vr/doc_classes/               @godotengine/xr @godotengine/documentation
/modules/openxr/                              @godotengine/xr
/modules/openxr/doc_classes/                  @godotengine/xr @godotengine/documentation
/modules/webxr/                               @godotengine/xr
/modules/webxr/doc_classes/                   @godotengine/xr @godotengine/documentation

## Misc
/modules/csg/                                 @godotengine/3d-nodes
/modules/csg/doc_classes/                     @godotengine/3d-nodes @godotengine/documentation
/modules/csg/icons/                           @godotengine/3d-nodes @godotengine/usability
/modules/gridmap/                             @godotengine/3d-nodes
/modules/gridmap/doc_classes/                 @godotengine/3d-nodes @godotengine/documentation
/modules/gridmap/icons/                       @godotengine/3d-nodes @godotengine/usability
/modules/navigation_2d/                       @godotengine/navigation
/modules/navigation_3d/                       @godotengine/navigation
/modules/noise/                               @godotengine/core
/modules/noise/doc_classes/                   @godotengine/core @godotengine/documentation
/modules/noise/icons/                         @godotengine/core @godotengine/usability
/modules/noise/tests/                         @godotengine/core @godotengine/tests
/modules/regex/                               @godotengine/core
/modules/regex/doc_classes/                   @godotengine/core @godotengine/documentation
/modules/regex/icons/                         @godotengine/core @godotengine/usability
/modules/regex/tests/                         @godotengine/core @godotengine/tests
/modules/zip/                                 @godotengine/core
/modules/zip/doc_classes/                     @godotengine/core @godotengine/documentation
/modules/zip/tests                            @godotengine/core @godotengine/tests

# Platform

/platform/android/                            @godotengine/android
/platform/android/doc_classes/                @godotengine/android @godotengine/documentation
/platform/ios/                                @godotengine/ios
/platform/ios/doc_classes/                    @godotengine/ios @godotengine/documentation
/platform/linuxbsd/                           @godotengine/linux-bsd
/platform/linuxbsd/doc_classes/               @godotengine/linux-bsd @godotengine/documentation
/platform/macos/                              @godotengine/macos
/platform/macos/doc_classes/                  @godotengine/macos @godotengine/documentation
/platform/web/                                @godotengine/web
/platform/web/doc_classes/                    @godotengine/web @godotengine/documentation
/platform/windows/                            @godotengine/windows
/platform/windows/doc_classes/                @godotengine/windows @godotengine/documentation

# Scene

/scene/2d/                                    @godotengine/2d-nodes
/scene/2d/physics/                            @godotengine/2d-nodes @godotengine/physics
/scene/3d/                                    @godotengine/3d-nodes
/scene/3d/physics/                            @godotengine/3d-nodes @godotengine/physics
/scene/animation/                             @godotengine/animation
/scene/audio/                                 @godotengine/audio
/scene/debugger/                              @godotengine/debugger
/scene/gui/                                   @godotengine/gui-nodes
/scene/main/                                  @godotengine/core
/scene/resources/2d/                          @godotengine/2d-nodes
/scene/resources/3d/                          @godotengine/3d-nodes
/scene/resources/animated*                    @godotengine/animation
/scene/resources/animation*                   @godotengine/animation
/scene/resources/audio*                       @godotengine/audio
/scene/resources/font*                        @godotengine/gui-nodes
/scene/resources/shader*                      @godotengine/shaders
/scene/resources/text_*                       @godotengine/gui-nodes
/scene/resources/visual_shader*               @godotengine/shaders
/scene/theme/                                 @godotengine/gui-nodes
/scene/theme/icons/                           @godotengine/gui-nodes @godotengine/usability

# Servers

/servers/**/audio_*                           @godotengine/audio
/servers/**/camera_*                          @godotengine/xr
/servers/**/debugger_*                        @godotengine/debugger
/servers/**/navigation_*                      @godotengine/navigation
/servers/**/physics_*                         @godotengine/physics
/servers/**/rendering_*                       @godotengine/rendering
/servers/**/text_*                            @godotengine/gui-nodes
/servers/**/xr_*                              @godotengine/xr
/servers/audio/                               @godotengine/audio
/servers/camera/                              @godotengine/xr
/servers/debugger/                            @godotengine/debugger
/servers/navigation/                          @godotengine/navigation
/servers/rendering/                           @godotengine/rendering
/servers/text/                                @godotengine/gui-nodes
/servers/xr/                                  @godotengine/xr

# Tests

/tests/                                       @godotengine/tests

# Thirdparty

/thirdparty/                                  @godotengine/buildsystem

# Buildsystem (After everything to catch all)

/*.*                                          @godotengine/buildsystem
*.py                                          @godotengine/buildsystem
SConstruct                                    @godotengine/buildsystem
SCsub                                         @godotengine/buildsystem
