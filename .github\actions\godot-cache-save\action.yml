name: Save Godot build cache
description: Save Godot build cache.
inputs:
  cache-name:
    description: The cache base name (job name by default).
    default: ${{ github.job }}
  scons-cache:
    description: The SCons cache path.
    default: ${{ github.workspace }}/.scons_cache/

runs:
  using: composite
  steps:
    - name: Purge files before timestamp
      shell: sh
      run: misc/scripts/purge_cache.py ${{ env.CACHE_TIMESTAMP }} "${{ inputs.scons-cache }}"

    - name: Save SCons cache directory
      uses: actions/cache/save@v4
      with:
        path: ${{ inputs.scons-cache }}
        key: ${{ inputs.cache-name }}|${{ github.ref_name }}|${{ github.sha }}
