name: Build godot-cpp
description: Build godot-cpp with the provided options.

inputs:
  bin:
    description: Path to the Godot binary.
    required: true
    type: string
  scons-flags:
    description: Additional SCons flags.
    type: string
  scons-cache:
    description: The SCons cache path.
    default: ${{ github.workspace }}/.scons_cache/
    type: string
  godot-cpp-branch:
    description: The godot-cpp branch.
    default: master
    type: string

runs:
  using: composite
  steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive
        repository: godotengine/godot-cpp
        ref: ${{ inputs.godot-cpp-branch }}
        path: godot-cpp

    - name: Extract API
      shell: sh
      run: ${{ inputs.bin }} --headless --dump-gdextension-interface --dump-extension-api

    - name: SCons Build
      shell: sh
      run: scons --directory=./godot-cpp/test "gdextension_dir=${{ github.workspace }}" ${{ inputs.scons-flags }}
