/**************************************************************************/
/*  debugger_marshalls.h                                                  */
/**************************************************************************/
/*                         This file is part of:                          */
/*                             GODOT ENGINE                               */
/*                        https://godotengine.org                         */
/**************************************************************************/
/* Copyright (c) 2014-present Godot Engine contributors (see AUTHORS.md). */
/* Copyright (c) 2007-2014 <PERSON>, <PERSON>.                  */
/*                                                                        */
/* Permission is hereby granted, free of charge, to any person obtaining  */
/* a copy of this software and associated documentation files (the        */
/* "Software"), to deal in the Software without restriction, including    */
/* without limitation the rights to use, copy, modify, merge, publish,    */
/* distribute, sublicense, and/or sell copies of the Software, and to     */
/* permit persons to whom the Software is furnished to do so, subject to  */
/* the following conditions:                                              */
/*                                                                        */
/* The above copyright notice and this permission notice shall be         */
/* included in all copies or substantial portions of the Software.        */
/*                                                                        */
/* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,        */
/* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF     */
/* MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. */
/* IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY   */
/* CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,   */
/* TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE      */
/* SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.                 */
/**************************************************************************/

#pragma once

#include "core/input/shortcut.h"
#include "core/object/script_language.h"

struct DebuggerMarshalls {
	struct ScriptStackVariable {
		String name;
		Variant value;
		int type = -1;
		int var_type = -1;

		Array serialize(int max_size = 1 << 20); // 1 MiB default.
		bool deserialize(const Array &p_arr);
	};

	struct ScriptStackDump {
		List<ScriptLanguage::StackInfo> frames;
		ScriptStackDump() {}

		Array serialize();
		bool deserialize(const Array &p_arr);
	};

	struct OutputError {
		int hr = -1;
		int min = -1;
		int sec = -1;
		int msec = -1;
		String source_file;
		String source_func;
		int source_line = -1;
		String error;
		String error_descr;
		bool warning = false;
		Vector<ScriptLanguage::StackInfo> callstack;

		Array serialize();
		bool deserialize(const Array &p_arr);
	};

	static Array serialize_key_shortcut(const Ref<Shortcut> &p_shortcut);
	static Ref<Shortcut> deserialize_key_shortcut(const Array &p_keys);
};
