name: 🏁 Windows Builds
on:
  workflow_call:

# Global Settings
env:
  SCONS_FLAGS: >-
    dev_mode=yes
    module_text_server_fb_enabled=yes
    debug_symbols=no
    d3d12=yes
    "angle_libs=${{ github.workspace }}/"
    "accesskit_sdk_path=${{ github.workspace }}/accesskit-c-0.17.0/"
  SCONS_CACHE_MSVC_CONFIG: true
  PYTHONIOENCODING: utf8

jobs:
  build-windows:
    # Windows 10 with latest image
    runs-on: windows-latest
    name: ${{ matrix.name }}
    timeout-minutes: 120
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Editor (target=editor)
            cache-name: windows-editor
            target: editor
            scons-flags: >-
              windows_subsystem=console
              vsproj=yes
              vsproj_gen_only=no
            bin: ./bin/godot.windows.editor.x86_64.exe
            compiler: msvc

          - name: Editor w/ clang-cl (target=editor, use_llvm=yes)
            cache-name: windows-editor-clang
            target: editor
            scons-flags: >-
              windows_subsystem=console
              use_llvm=yes
            bin: ./bin/godot.windows.editor.x86_64.llvm.exe
            compiler: clang

          - name: Template (target=template_release)
            cache-name: windows-template
            target: template_release
            bin: ./bin/godot.windows.template_release.x86_64.console.exe
            compiler: msvc

          - name: Template w/ GCC (target=template_release, use_mingw=yes)
            cache-name: windows-template-gcc
            # MinGW takes MUCH longer to compile; save time by only targeting Template.
            target: template_release
            scons-flags: use_mingw=yes
            bin: ./bin/godot.windows.template_release.x86_64.console.exe
            compiler: gcc

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Restore Godot build cache
        uses: ./.github/actions/godot-cache-restore
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Setup Python and SCons
        uses: ./.github/actions/godot-deps

      - name: Download Direct3D 12 SDK components
        run: python ./misc/scripts/install_d3d12_sdk_windows.py

      - name: Download pre-built ANGLE static libraries
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: godotengine/godot-angle-static
          version: tags/chromium/6601.2
          file: godot-angle-static-x86_64-${{ matrix.compiler == 'gcc' && 'gcc' || 'msvc' }}-release.zip
          target: angle/angle.zip

      - name: Extract pre-built ANGLE static libraries
        run: Expand-Archive -Force angle/angle.zip ${{ github.workspace }}/

      - name: Download pre-built AccessKit
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: AccessKit/accesskit-c
          version: tags/0.17.0
          file: accesskit-c-0.17.0.zip
          target: accesskit-c-0.17.0/accesskit_c.zip

      - name: Extract pre-built AccessKit
        run: unzip -o accesskit-c-0.17.0/accesskit_c.zip

      - name: Compilation
        uses: ./.github/actions/godot-build
        with:
          scons-flags: ${{ env.SCONS_FLAGS }} ${{ matrix.scons-flags }}
          platform: windows
          target: ${{ matrix.target }}

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Prepare artifact
        if: matrix.compiler == 'msvc'
        run: |
          Remove-Item bin/* -Include *.exp,*.lib,*.pdb -Force

      - name: Upload artifact
        if: matrix.compiler == 'msvc'
        uses: ./.github/actions/upload-artifact
        with:
          name: ${{ matrix.cache-name }}

      - name: Unit tests
        run: |
          ${{ matrix.bin }} --version
          ${{ matrix.bin }} --help
          ${{ matrix.bin }} --test --force-colors
