#!/usr/bin/env python
from misc.utility.scons_hints import *

Import("env")

env_crypto = env.Clone()

is_builtin = env["builtin_mbedtls"]
has_module = env["module_mbedtls_enabled"]
thirdparty_obj = []

if is_builtin or not has_module:
    # Use our headers for builtin or if the module is not going to be compiled.
    # We decided not to depend on system mbedtls just for these few files that can
    # be easily extracted.
    env_crypto.Prepend(CPPEXTPATH=["#thirdparty/mbedtls/include"])

# MbedTLS core functions (for CryptoCore).
# If the mbedtls module is compiled we don't need to add the .c files with our
# custom config since they will be built by the module itself.
# Only if the module is not enabled, we must compile here the required sources
# to make a "light" build with only the necessary mbedtls files.
if not has_module:
    # Minimal mbedTLS config file
    config_path = "thirdparty/mbedtls/include/godot_core_mbedtls_config.h"
    config_path = f"<{config_path}>" if env_crypto["ninja"] and env_crypto.msvc else f'\\"{config_path}\\"'
    env_crypto.Append(CPPDEFINES=[("MBEDTLS_CONFIG_FILE", config_path)])
    # Build minimal mbedTLS library (MD5/SHA/Base64/AES).
    env_thirdparty = env_crypto.Clone()
    env_thirdparty.disable_warnings()
    thirdparty_mbedtls_dir = "#thirdparty/mbedtls/library/"
    thirdparty_mbedtls_sources = [
        "aes.c",
        "base64.c",
        "constant_time.c",
        "ctr_drbg.c",
        "entropy.c",
        "md.c",
        "md5.c",
        "sha1.c",
        "sha256.c",
        "godot_core_mbedtls_platform.c",
    ]
    thirdparty_mbedtls_sources = [thirdparty_mbedtls_dir + file for file in thirdparty_mbedtls_sources]
    env_thirdparty.add_source_files(thirdparty_obj, thirdparty_mbedtls_sources)
    # Needed to force rebuilding the library when the configuration file is updated.
    env_thirdparty.Depends(thirdparty_obj, "#thirdparty/mbedtls/include/godot_core_mbedtls_config.h")
    env.core_sources += thirdparty_obj
elif is_builtin:
    # Module mbedTLS config file
    config_path = "thirdparty/mbedtls/include/godot_module_mbedtls_config.h"
    config_path = f"<{config_path}>" if env_crypto["ninja"] and env_crypto.msvc else f'\\"{config_path}\\"'
    env_crypto.Append(CPPDEFINES=[("MBEDTLS_CONFIG_FILE", config_path)])
    # Needed to force rebuilding the core files when the configuration file is updated.
    thirdparty_obj = ["#thirdparty/mbedtls/include/godot_module_mbedtls_config.h"]

# Godot source files

core_obj = []

env_crypto.add_source_files(core_obj, "*.cpp")
env.core_sources += core_obj

# Needed to force rebuilding the core files when the thirdparty library is updated.
env.Depends(core_obj, thirdparty_obj)
