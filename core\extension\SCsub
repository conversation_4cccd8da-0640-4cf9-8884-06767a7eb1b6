#!/usr/bin/env python
from misc.utility.scons_hints import *

Import("env")

import make_interface_dumper
import make_wrappers

env.CommandNoCache(["ext_wrappers.gen.inc"], "make_wrappers.py", env.Run(make_wrappers.run))
env.CommandNoCache(
    "gdextension_interface_dump.gen.h",
    ["gdextension_interface.h", "make_interface_dumper.py"],
    env.Run(make_interface_dumper.run),
)

env_extension = env.Clone()

env_extension.add_source_files(env.core_sources, "*.cpp")
