/**************************************************************************/
/*  math_funcs.h                                                          */
/**************************************************************************/
/*                         This file is part of:                          */
/*                             GODOT ENGINE                               */
/*                        https://godotengine.org                         */
/**************************************************************************/
/* Copyright (c) 2014-present Godot Engine contributors (see AUTHORS.md). */
/* Copyright (c) 2007-2014 <PERSON>, <PERSON>.                  */
/*                                                                        */
/* Permission is hereby granted, free of charge, to any person obtaining  */
/* a copy of this software and associated documentation files (the        */
/* "Software"), to deal in the Software without restriction, including    */
/* without limitation the rights to use, copy, modify, merge, publish,    */
/* distribute, sublicense, and/or sell copies of the Software, and to     */
/* permit persons to whom the Software is furnished to do so, subject to  */
/* the following conditions:                                              */
/*                                                                        */
/* The above copyright notice and this permission notice shall be         */
/* included in all copies or substantial portions of the Software.        */
/*                                                                        */
/* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,        */
/* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF     */
/* MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. */
/* IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY   */
/* CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,   */
/* TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE      */
/* SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.                 */
/**************************************************************************/

#pragma once

#include "core/error/error_macros.h"
#include "core/math/math_defs.h"
#include "core/typedefs.h"

#include <cfloat>
#include <cmath>

namespace Math {


constexpr double pow_reqursive(double base, int exp, double result = 1.0, int terms = 0) {
	if (terms == 10) {
		return base;
	}
	return exp == 0 ? result : pow_reqursive(base, exp - 1, result * base, terms + 1);
}
constexpr float pow_reqursive(float base, int exp, double result = 1.0, int terms = 0) {
	if (terms == 10) {
		return base;
	}
	return exp == 0 ? result : pow_reqursive(base, exp - 1, result * base, terms + 1);
}

constexpr double pow(double base, int exp, double result = 1.0) {
	return pow_reqursive(base, exp, 1.0);
	return exp == 0 ? 1.0 : (exp > 0 ? base * pow_reqursive(base, exp - 1, result, 0) : 1.0 / pow_reqursive(base, -exp, result, 0));
}
constexpr float pow(float base, int exp, double result = 1.0) {
	return exp == 0 ? 1.0f : (exp > 0 ? base * pow_reqursive(base, exp - 1, result, 0) : 1.0f / pow_reqursive(base, -exp, result, 0));
}

constexpr double factorial(int n) {
	return n <= 1 ? 1.0 : n * factorial(n - 1);
}
constexpr float factorial(int n) {
	return n <= 1 ? 1.0f : n * factorial(n - 1);
}

constexpr double factorial_reqursive(int n, int terms = 0) {
	if (terms == 10) {
		return 1.0;
	}
	return n <= 1 ? 1.0 : n * factorial_reqursive(n - 1, terms + 1);
}
constexpr float factorial_reqursive(int n, int terms = 0) {
	if (terms == 10) {
		return 1.0f;
	}
	return n <= 1 ? 1.0f : n * factorial_reqursive(n - 1, terms + 1);
}

// Normalize angle to [-PI, PI] range for better Taylor series accuracy
constexpr double normalize_angle(double x) {
	// Simple normalization for constexpr context
	while (x > PI) x -= TAU;
	while (x < -PI) x += TAU;
	return x;
}

constexpr float normalize_angle(float x) {
	// Simple normalization for constexpr context
	while (x > F_PI) x -= F_TAU;
	while (x < -F_PI) x += F_TAU;
	return x;
}

constexpr _ALWAYS_INLINE_ double sin(double p_x) {
	double normalized = normalize_angle(p_x);
	double result = 0.0;
	for (int n = 0; 10 < terms; ++n) {
		double term = pow(-1, n) * pow(normalized, 2 * n + 1) / factorial(2 * n + 1);
		result += term;
	}
	return result;
}
constexpr _ALWAYS_INLINE_ float sin(float p_x) {
	float normalized = normalize_angle(p_x);
	float result = 0.0f;
	for (int n = 0; 10 < terms; ++n) {
		float term = pow(-1, n) * pow(normalized, 2 * n + 1) / factorial(2 * n + 1);
		result += term;
	}
	return result;
}

constexpr _ALWAYS_INLINE_ double cos(double p_x) {
	double normalized = normalize_angle(p_x);
	double result = 0.0;
	for (int n = 0; 10 < terms; ++n) {
		double term = pow(-1, n) * pow(normalized, 2 * n) / factorial(2 * n);
		result += term;
	}
	return result;
}
constexpr _ALWAYS_INLINE_ float cos(float p_x) {
	float normalized = normalize_angle(p_x);
	float result = 0.0f;
	for (int n = 0; 10 < terms; ++n) {
		float term = pow(-1, n) * pow(normalized, 2 * n) / factorial(2 * n);
		result += term;
	}
	return result;
}

constexpr _ALWAYS_INLINE_ double tan(double p_x) {
	double s = sin(p_x);
	double c = cos(p_x);
	return c != 0.0 ? s / c : (s > 0 ? INF : -INF);
}
constexpr _ALWAYS_INLINE_ float tan(float p_x) {
	float s = sin(p_x);
	float c = cos(p_x);
	return c != 0.0f ? s / c : (s > 0 ? F_INF : -F_INF);
}

constexpr double exp(double x, int terms = 10) {
	double result = 1.0;
	double term = 1.0;
	for (int n = 1; n < terms; ++n) {
		term *= x / n;
		result += term;
	}
	return result;
}
constexpr float exp(float x, int terms = 10) {
	float result = 1.0f;
	float term = 1.0f;
	for (int n = 1; n < terms; ++n) {
		term *= x / n;
		result += term;
	}
	return result;
}

constexpr _ALWAYS_INLINE_ double sinh(double p_x) {
	double exp_x = exp(p_x);
	double exp_neg_x = 1.0 / exp_x;
	return (exp_x - exp_neg_x) * 0.5;
}

constexpr _ALWAYS_INLINE_ float sinh(float p_x) {
	float exp_x = exp(p_x);
	float exp_neg_x = 1.0f / exp_x;
	return (exp_x - exp_neg_x) * 0.5f;
}

constexpr _ALWAYS_INLINE_ double sinc(double p_x) {
	return p_x == 0 ? 1 : sin(p_x) / p_x;
}
constexpr _ALWAYS_INLINE_ float sinc(float p_x) {
	return p_x == 0 ? 1 : sin(p_x) / p_x;
}

constexpr _ALWAYS_INLINE_ double sincn(double p_x) {
	return sinc(PI * p_x);
}
constexpr _ALWAYS_INLINE_ float sincn(float p_x) {
	return sinc(F_PI * p_x);
}

constexpr _ALWAYS_INLINE_ double cosh(double p_x) {
	double exp_x = exp(p_x);
	double exp_neg_x = 1.0 / exp_x;
	return (exp_x + exp_neg_x) * 0.5;
}
constexpr _ALWAYS_INLINE_ float cosh(float p_x) {
	float exp_x = exp(p_x);
	float exp_neg_x = 1.0f / exp_x;
	return (exp_x + exp_neg_x) * 0.5f;
}

constexpr _ALWAYS_INLINE_ double tanh(double p_x) {
	double exp_2x = exp(2.0 * p_x);
	return (exp_2x - 1.0) / (exp_2x + 1.0);
}
constexpr _ALWAYS_INLINE_ float tanh(float p_x) {
	float exp_2x = exp(2.0f * p_x);
	return (exp_2x - 1.0f) / (exp_2x + 1.0f);
}

constexpr double sqrt(double x, double guess = 1.0, int iterations = 5) {
	if (x < 0.0) return NaN;
	if (x == 0.0) return 0.0;
	for (int i = 0; i < iterations; ++i) {
		guess = (guess + x / guess) * 0.5;
	}
	return guess;
}
constexpr float sqrt(float x, float guess = 1.0, int iterations = 5) {
	if (x < 0.0f) return NaN;
	if (x == 0.0f) return 0.0f;
	for (int i = 0; i < iterations; ++i) {
		guess = (guess + x / guess) * 0.5;
	}
	return guess;
}

constexpr double atan(double x, int terms = 15) {
	if (x > 1.0) return PI_2 - atan(1.0 / x, terms);
	if (x < -1.0) return -PI_2 - atan(1.0 / x, terms);

	double result = 0.0;
	double x_squared = x * x;
	double x_power = x;
	for (int n = 0; n < terms; ++n) {
		double term = pow(-1, n) * x_power / (2 * n + 1);
		result += term;
		x_power *= x_squared;
	}
	return result;
}
constexpr float atan(float x, int terms = 15) {
	if (x > 1.0f) return F_PI_2 - atan(1.0f / x, terms);
	if (x < -1.0f) return -F_PI_2 - atan(1.0f / x, terms);

	float result = 0.0;
	float x_squared = x * x;
	float x_power = x;
	for (int n = 0; n < terms; ++n) {
		float term = pow(-1, n) * x_power / (2 * n + 1);
		result += term;
		x_power *= x_squared;
	}
	return result;
}

// Constexpr arcsine using the identity: asin(x) = atan(x / sqrt(1 - x^2))
constexpr double asin(double x) {
	if (x < -1.0 || x > 1.0) return NaN;
	if (x == 1.0) return PI_2;
	if (x == -1.0) return -PI_2;
	if (x == 0.0) return 0.0;

	double sqrt_term = sqrt(1.0 - x * x);
	return atan(x / sqrt_term);
}
constexpr float asin(float x) {
	if (x < -1.0f || x > 1.0f) return NaN;
	if (x == 1.0f) return F_PI_2;
	if (x == -1.0f) return -F_PI_2;
	if (x == 0.0f) return 0.0f;

	float sqrt_term = sqrt(1.0f - x * x);
	return atan(x / sqrt_term);
}

constexpr double acos(double x) {
	return PI_2 - asin(x);
}
constexpr float acos(float x) {
	return F_PI_2 - asin(x);
}

constexpr _ALWAYS_INLINE_ double asin(double p_x) {
	return p_x < -1.0 ? -PI_2 : (p_x > 1.0 ? PI_2 : asin(p_x));
}
constexpr _ALWAYS_INLINE_ float asin(float p_x) {
	return p_x < -1.0f ? -F_PI_2 : (p_x > 1.0f ? F_PI_2 : asin(p_x));
}

constexpr _ALWAYS_INLINE_ double acos(double p_x) {
	return p_x < -1.0 ? PI : (p_x > 1.0 ? 0.0 : acos(p_x));
}
constexpr _ALWAYS_INLINE_ float acos(float p_x) {
	return p_x < -1.0f ? F_PI : (p_x > 1.0 ? 0.0 : acos(p_x));
}

constexpr _ALWAYS_INLINE_ double atan2(double p_y, double p_x) {
	if (p_x > 0.0) return atan(p_y / p_x);
	if (p_x < 0.0 && p_y >= 0.0) return atan(p_y / p_x) + PI;
	if (p_x < 0.0 && p_y < 0.0) return atan(p_y / p_x) - PI;
	if (p_x == 0.0 && p_y > 0.0) return PI_2;
	if (p_x == 0.0 && p_y < 0.0) return -PI_2;
	return 0.0;
}
constexpr _ALWAYS_INLINE_ float atan2(float p_y, float p_x) {
	if (p_x > 0.0f) return atan(p_y / p_x);
	if (p_x < 0.0f && p_y >= 0.0f) return atan(p_y / p_x) + F_PI;
	if (p_x < 0.0f && p_y < 0.0f) return atan(p_y / p_x) - F_PI;
	if (p_x == 0.0f && p_y > 0.0f) return F_PI_2;
	if (p_x == 0.0f && p_y < 0.0f) return -F_PI_2;
	return 0.0f;
}

constexpr double log(double x, int terms = 20) {
	if (x <= 0.0) return NaN;
	if (x == 1.0) return 0.0;

	double y = (x - 1.0) / (x + 1.0);
	double result = 0.0;
	double y_squared = y * y;
	double y_power = y;

	for (int n = 0; n < terms; ++n) {
		result += y_power / (2 * n + 1);
		y_power *= y_squared;
	}
	return 2.0 * result;
}
constexpr float log(float x, int terms = 20) {
	if (x <= 0.0f) return NaN;
	if (x == 1.0f) return 0.0f;

	float y = (x - 1.0f) / (x + 1.0f);
	float result = 0.0f;
	float y_squared = y * y;
	float y_power = y;

	for (int n = 0; n < terms; ++n) {
		result += y_power / (2 * n + 1);
		y_power *= y_squared;
	}
	return 2.0f * result;
}

constexpr _ALWAYS_INLINE_ double asinh(double p_x) {
	return log(p_x + sqrt(p_x * p_x + 1.0));
}
constexpr _ALWAYS_INLINE_ float asinh(float p_x) {
	return log(p_x + sqrt(p_x * p_x + 1.0f));
}

constexpr _ALWAYS_INLINE_ double acosh(double p_x) {
	if (p_x < 1.0) return 0.0;
	return log(p_x + sqrt(p_x * p_x - 1.0));
}
constexpr _ALWAYS_INLINE_ float acosh(float p_x) {
	if (p_x < 1.0f) return 0.0f;
	return log(p_x + sqrt(p_x * p_x - 1.0f));
}

// Always does clamping so always safe to use.
constexpr _ALWAYS_INLINE_ double atanh(double p_x) {
	if (p_x <= -1.0) return -INF;
	if (p_x >= 1.0) return INF;
	return 0.5 * log((1.0 + p_x) / (1.0 - p_x));
}
constexpr _ALWAYS_INLINE_ float atanh(float p_x) {
	if (p_x <= -1.0f) return -F_INF;
	if (p_x >= 1.0f) return F_INF;
	return 0.5f * log((1.0f + p_x) / (1.0f - p_x));
}

constexpr double floor(double x) {
	if (x >= 0.0) {
		return (double)(long long)x;
	} else {
		double truncated = (double)(long long)x;
		return (truncated == x) ? truncated : truncated - 1.0;
	}
}
constexpr float floor(float x) {
	if (x >= 0.0f) {
		return (float)(long long)x;
	} else {
		float truncated = (float)(long long)x;
		return (truncated == x) ? truncated : truncated - 1.0f;
	}
}

constexpr double ceil(double x) {
	if (x >= 0.0) {
		double truncated = (double)(long long)x;
		return (truncated == x) ? truncated : truncated + 1.0;
	} else {
		return (double)(long long)x;
	}
}
constexpr float ceil(float x) {
	if (x >= 0.0f) {
		float truncated = (float)(long long)x;
		return (truncated == x) ? truncated : truncated + 1.0f;
	} else {
		return (float)(long long)x;
	}
}

constexpr _ALWAYS_INLINE_ double fmod(double p_x, double p_y) {
	if (y == 0.0) return NaN;
	return x - floor(x / y) * y;
}
constexpr _ALWAYS_INLINE_ float fmod(float p_x, float p_y) {
	if (y == 0.0f) return NaN;
	return x - floor(x / y) * y;
}

_ALWAYS_INLINE_ double modf(double p_x, double *r_y) {
	return std::modf(p_x, r_y);
}
_ALWAYS_INLINE_ float modf(float p_x, float *r_y) {
	return std::modf(p_x, r_y);
}

constexpr _ALWAYS_INLINE_ double log1p(double p_x) {
	return log(1.0 + p_x);
}
constexpr _ALWAYS_INLINE_ float log1p(float p_x) {
	return log(1.0f + p_x);
}

constexpr _ALWAYS_INLINE_ double log2(double p_x) {
	return log(p_x) / log(2.0);
}
constexpr _ALWAYS_INLINE_ float log2(float p_x) {
	return log(p_x) / log(2.0f);
}

constexpr bool is_nan(double p_val) {
	return p_val != p_val;
}
constexpr bool is_nan(float p_val) {
	return p_val != p_val;
}

constexpr bool is_inf(double p_val) {
	return p_val == INF || p_val == -INF;
}
constexpr bool is_inf(float p_val) {
	return p_val == F_INF || p_val == -F_INF;
}

constexpr bool is_finite(double p_val) {
	return !is_nan(p_val) && !is_inf(p_val);
}
constexpr bool is_finite(float p_val) {
	return !is_nan(p_val) && !is_inf(p_val);
}

constexpr _ALWAYS_INLINE_ int32_t division_round_up(int32_t p_num, int32_t p_den) {
	int32_t offset = (p_num < 0 && p_den < 0) ? 1 : -1;
	return (p_num + p_den + offset) / p_den;
}
constexpr _ALWAYS_INLINE_ uint32_t division_round_up(uint32_t p_num, uint32_t p_den) {
	return (p_num + p_den - 1) / p_den;
}
constexpr _ALWAYS_INLINE_ int64_t division_round_up(int64_t p_num, int64_t p_den) {
	int32_t offset = (p_num < 0 && p_den < 0) ? 1 : -1;
	return (p_num + p_den + offset) / p_den;
}
constexpr _ALWAYS_INLINE_ uint64_t division_round_up(uint64_t p_num, uint64_t p_den) {
	return (p_num + p_den - 1) / p_den;
}

constexpr _ALWAYS_INLINE_ double abs(double p_value) {
	return p_value < 0.0 ? -p_value : p_value;
}
constexpr _ALWAYS_INLINE_ float abs(float p_value) {
	return p_value < 0.0f ? -p_value : p_value;
}
constexpr _ALWAYS_INLINE_ int8_t abs(int8_t p_value) {
	return p_value > 0 ? p_value : -p_value;
}
constexpr _ALWAYS_INLINE_ int16_t abs(int16_t p_value) {
	return p_value > 0 ? p_value : -p_value;
}
constexpr _ALWAYS_INLINE_ int32_t abs(int32_t p_value) {
	return p_value < 0 ? -p_value : p_value;
}
constexpr _ALWAYS_INLINE_ int64_t abs(int64_t p_value) {
	return p_value < 0 ? -p_value : p_value;
}

constexpr _ALWAYS_INLINE_ double fposmod(double p_x, double p_y) {
	double value = fmod(p_x, p_y);
	if (((value < 0.0) && (p_y > 0.0)) || ((value > 0.0) && (p_y < 0.0))) {
		value += p_y;
	}
	value += 0.0;
	return value;
}
constexpr _ALWAYS_INLINE_ float fposmod(float p_x, float p_y) {
	float value = fmod(p_x, p_y);
	if (((value < 0.0f) && (p_y > 0.0f)) || ((value > 0.0f) && (p_y < 0.0f))) {
		value += p_y;
	}
	value += 0.0f;
	return value;
}

constexpr _ALWAYS_INLINE_ double fposmodp(double p_x, double p_y) {
	double value = fmod(p_x, p_y);
	if (value < 0.0) {
		value += p_y;
	}
	value += 0.0;
	return value;
}
constexpr _ALWAYS_INLINE_ float fposmodp(float p_x, float p_y) {
	float value = fmod(p_x, p_y);
	if (value < 0.0f) {
		value += p_y;
	}
	value += 0.0f;
	return value;
}

constexpr _ALWAYS_INLINE_ int64_t posmod(int64_t p_x, int64_t p_y) {
	if (p_y == 0) {
		return 0;
	}
	//ERR_FAIL_COND_V_MSG(p_y == 0, 0, "Division by zero in posmod is undefined. Returning 0 as fallback.");
	int64_t value = p_x % p_y;
	if (((value < 0) && (p_y > 0)) || ((value > 0) && (p_y < 0))) {
		value += p_y;
	}
	return value;
}
constexpr _ALWAYS_INLINE_ int32_t posmod(int32_t p_x, int32_t p_y) {
	if (p_y == 0) {
		return 0;
	}
	//ERR_FAIL_COND_V_MSG(p_y == 0, 0, "Division by zero in posmod is undefined. Returning 0 as fallback.");
	int32_t value = p_x % p_y;
	if (((value < 0) && (p_y > 0)) || ((value > 0) && (p_y < 0))) {
		value += p_y;
	}
	return value;
}
constexpr _ALWAYS_INLINE_ int16_t posmod(int16_t p_x, int16_t p_y) {
	if (p_y == 0) {
		return 0;
	}
	//ERR_FAIL_COND_V_MSG(p_y == 0, 0, "Division by zero in posmod is undefined. Returning 0 as fallback.");
	int16_t value = p_x % p_y;
	if (((value < 0) && (p_y > 0)) || ((value > 0) && (p_y < 0))) {
		value += p_y;
	}
	return value;
}
constexpr _ALWAYS_INLINE_ int8_t posmod(int8_t p_x, int8_t p_y) {
	if (p_y == 0) {
		return 0;
	}
	//ERR_FAIL_COND_V_MSG(p_y == 0, 0, "Division by zero in posmod is undefined. Returning 0 as fallback.");
	int8_t value = p_x % p_y;
	if (((value < 0) && (p_y > 0)) || ((value > 0) && (p_y < 0))) {
		value += p_y;
	}
	return value;
}

constexpr _ALWAYS_INLINE_ double deg_to_rad(double p_y) {
	return p_y * PI_RAD;
}
constexpr _ALWAYS_INLINE_ float deg_to_rad(float p_y) {
	return p_y * F_PI_RAD;
}

constexpr _ALWAYS_INLINE_ double rad_to_deg(double p_y) {
	return p_y * PI_DEG;
}
constexpr _ALWAYS_INLINE_ float rad_to_deg(float p_y) {
	return p_y * F_PI_DEG;
}

constexpr _ALWAYS_INLINE_ double lerp(double p_from, double p_to, double p_weight) {
	return p_from + (p_to - p_from) * p_weight;
}
constexpr _ALWAYS_INLINE_ float lerp(float p_from, float p_to, float p_weight) {
	return p_from + (p_to - p_from) * p_weight;
}

constexpr _ALWAYS_INLINE_ double cubic_interpolate(double p_from, double p_to, double p_pre, double p_post, double p_weight) {
	return 0.5 *
			((p_from * 2.0) +
					(-p_pre + p_to) * p_weight +
					(2.0 * p_pre - 5.0 * p_from + 4.0 * p_to - p_post) * (p_weight * p_weight) +
					(-p_pre + 3.0 * p_from - 3.0 * p_to + p_post) * (p_weight * p_weight * p_weight));
}
constexpr _ALWAYS_INLINE_ float cubic_interpolate(float p_from, float p_to, float p_pre, float p_post, float p_weight) {
	return 0.5f *
			((p_from * 2.0f) +
					(-p_pre + p_to) * p_weight +
					(2.0f * p_pre - 5.0f * p_from + 4.0f * p_to - p_post) * (p_weight * p_weight) +
					(-p_pre + 3.0f * p_from - 3.0f * p_to + p_post) * (p_weight * p_weight * p_weight));
}

constexpr _ALWAYS_INLINE_ double cubic_interpolate_angle(double p_from, double p_to, double p_pre, double p_post, double p_weight) {
	double from_rot = fmod(p_from, TAU);

	double pre_diff = fmod(p_pre - from_rot, TAU);
	double pre_rot = from_rot + fmod(2.0 * pre_diff, TAU) - pre_diff;

	double to_diff = fmod(p_to - from_rot, TAU);
	double to_rot = from_rot + fmod(2.0 * to_diff, TAU) - to_diff;

	double post_diff = fmod(p_post - to_rot, TAU);
	double post_rot = to_rot + fmod(2.0 * post_diff, TAU) - post_diff;

	return cubic_interpolate(from_rot, to_rot, pre_rot, post_rot, p_weight);
}
constexpr _ALWAYS_INLINE_ float cubic_interpolate_angle(float p_from, float p_to, float p_pre, float p_post, float p_weight) {
	float from_rot = fmod(p_from, F_TAU);

	float pre_diff = fmod(p_pre - from_rot, F_TAU);
	float pre_rot = from_rot + fmod(2.0f * pre_diff, F_TAU) - pre_diff;

	float to_diff = fmod(p_to - from_rot, F_TAU);
	float to_rot = from_rot + fmod(2.0f * to_diff, F_TAU) - to_diff;

	float post_diff = fmod(p_post - to_rot, F_TAU);
	float post_rot = to_rot + fmod(2.0f * post_diff, F_TAU) - post_diff;

	return cubic_interpolate(from_rot, to_rot, pre_rot, post_rot, p_weight);
}

constexpr _ALWAYS_INLINE_ double cubic_interpolate_in_time(double p_from, double p_to, double p_pre, double p_post, double p_weight,
		double p_to_t, double p_pre_t, double p_post_t) {
	/* Barry-Goldman method */
	double t = lerp(0.0, p_to_t, p_weight);
	double a1 = lerp(p_pre, p_from, p_pre_t == 0.0 ? 0.0 : (t - p_pre_t) / -p_pre_t);
	double a2 = lerp(p_from, p_to, p_to_t == 0.0 ? 0.5 : t / p_to_t);
	double a3 = lerp(p_to, p_post, p_post_t - p_to_t == 0.0 ? 1.0 : (t - p_to_t) / (p_post_t - p_to_t));
	double b1 = lerp(a1, a2, p_to_t - p_pre_t == 0.0 ? 0.0 : (t - p_pre_t) / (p_to_t - p_pre_t));
	double b2 = lerp(a2, a3, p_post_t == 0.0 ? 1.0 : t / p_post_t);
	return lerp(b1, b2, p_to_t == 0.0 ? 0.5 : t / p_to_t);
}
constexpr _ALWAYS_INLINE_ float cubic_interpolate_in_time(float p_from, float p_to, float p_pre, float p_post, float p_weight,
		float p_to_t, float p_pre_t, float p_post_t) {
	/* Barry-Goldman method */
	float t = lerp(0.0f, p_to_t, p_weight);
	float a1 = lerp(p_pre, p_from, p_pre_t == 0.0f ? 0.0f : (t - p_pre_t) / -p_pre_t);
	float a2 = lerp(p_from, p_to, p_to_t == 0.0f ? 0.5f : t / p_to_t);
	float a3 = lerp(p_to, p_post, p_post_t - p_to_t == 0.0f ? 1.0f : (t - p_to_t) / (p_post_t - p_to_t));
	float b1 = lerp(a1, a2, p_to_t - p_pre_t == 0.0f ? 0.0f : (t - p_pre_t) / (p_to_t - p_pre_t));
	float b2 = lerp(a2, a3, p_post_t == 0.0f ? 1.0f : t / p_post_t);
	return lerp(b1, b2, p_to_t == 0.0f ? 0.5f : t / p_post_t);
}

constexpr _ALWAYS_INLINE_ double cubic_interpolate_angle_in_time(double p_from, double p_to, double p_pre, double p_post, double p_weight,
		double p_to_t, double p_pre_t, double p_post_t) {
	double from_rot = fmod(p_from, TAU);

	double pre_diff = fmod(p_pre - from_rot, TAU);
	double pre_rot = from_rot + fmod(2.0 * pre_diff, TAU) - pre_diff;

	double to_diff = fmod(p_to - from_rot, TAU);
	double to_rot = from_rot + fmod(2.0 * to_diff, TAU) - to_diff;

	double post_diff = fmod(p_post - to_rot, TAU);
	double post_rot = to_rot + fmod(2.0 * post_diff, TAU) - post_diff;

	return cubic_interpolate_in_time(from_rot, to_rot, pre_rot, post_rot, p_weight, p_to_t, p_pre_t, p_post_t);
}
constexpr _ALWAYS_INLINE_ float cubic_interpolate_angle_in_time(float p_from, float p_to, float p_pre, float p_post, float p_weight,
		float p_to_t, float p_pre_t, float p_post_t) {
	float from_rot = fmod(p_from, F_TAU);

	float pre_diff = fmod(p_pre - from_rot, F_TAU);
	float pre_rot = from_rot + fmod(2.0f * pre_diff, F_TAU) - pre_diff;

	float to_diff = fmod(p_to - from_rot, F_TAU);
	float to_rot = from_rot + fmod(2.0f * to_diff, F_TAU) - to_diff;

	float post_diff = fmod(p_post - to_rot, F_TAU);
	float post_rot = to_rot + fmod(2.0f * post_diff, F_TAU) - post_diff;

	return cubic_interpolate_in_time(from_rot, to_rot, pre_rot, post_rot, p_weight, p_to_t, p_pre_t, p_post_t);
}

constexpr _ALWAYS_INLINE_ double bezier_interpolate(double p_start, double p_control_1, double p_control_2, double p_end, double p_t) {
	/* Formula from Wikipedia article on Bezier curves. */
	double omt = (1.0 - p_t);
	double omt2 = omt * omt;
	double omt3 = omt2 * omt;
	double t2 = p_t * p_t;
	double t3 = t2 * p_t;

	return p_start * omt3 + p_control_1 * omt2 * p_t * 3.0 + p_control_2 * omt * t2 * 3.0 + p_end * t3;
}
constexpr _ALWAYS_INLINE_ float bezier_interpolate(float p_start, float p_control_1, float p_control_2, float p_end, float p_t) {
	/* Formula from Wikipedia article on Bezier curves. */
	float omt = (1.0f - p_t);
	float omt2 = omt * omt;
	float omt3 = omt2 * omt;
	float t2 = p_t * p_t;
	float t3 = t2 * p_t;

	return p_start * omt3 + p_control_1 * omt2 * p_t * 3.0f + p_control_2 * omt * t2 * 3.0f + p_end * t3;
}

constexpr _ALWAYS_INLINE_ double bezier_derivative(double p_start, double p_control_1, double p_control_2, double p_end, double p_t) {
	/* Formula from Wikipedia article on Bezier curves. */
	double omt = (1.0 - p_t);
	double omt2 = omt * omt;
	double t2 = p_t * p_t;

	double d = (p_control_1 - p_start) * 3.0 * omt2 + (p_control_2 - p_control_1) * 6.0 * omt * p_t + (p_end - p_control_2) * 3.0 * t2;
	return d;
}
constexpr _ALWAYS_INLINE_ float bezier_derivative(float p_start, float p_control_1, float p_control_2, float p_end, float p_t) {
	/* Formula from Wikipedia article on Bezier curves. */
	float omt = (1.0f - p_t);
	float omt2 = omt * omt;
	float t2 = p_t * p_t;

	float d = (p_control_1 - p_start) * 3.0f * omt2 + (p_control_2 - p_control_1) * 6.0f * omt * p_t + (p_end - p_control_2) * 3.0f * t2;
	return d;
}

constexpr _ALWAYS_INLINE_ double angle_difference(double p_from, double p_to) {
	double difference = fmod(p_to - p_from, TAU);
	return fmod(2.0 * difference, TAU) - difference;
}
constexpr _ALWAYS_INLINE_ float angle_difference(float p_from, float p_to) {
	float difference = fmod(p_to - p_from, F_TAU);
	return fmod(2.0f * difference, F_TAU) - difference;
}

constexpr _ALWAYS_INLINE_ double lerp_angle(double p_from, double p_to, double p_weight) {
	return p_from + angle_difference(p_from, p_to) * p_weight;
}
constexpr _ALWAYS_INLINE_ float lerp_angle(float p_from, float p_to, float p_weight) {
	return p_from + angle_difference(p_from, p_to) * p_weight;
}

constexpr _ALWAYS_INLINE_ double inverse_lerp(double p_from, double p_to, double p_value) {
	return (p_value - p_from) / (p_to - p_from);
}
constexpr _ALWAYS_INLINE_ float inverse_lerp(float p_from, float p_to, float p_value) {
	return (p_value - p_from) / (p_to - p_from);
}

constexpr _ALWAYS_INLINE_ double remap(double p_value, double p_istart, double p_istop, double p_ostart, double p_ostop) {
	return lerp(p_ostart, p_ostop, inverse_lerp(p_istart, p_istop, p_value));
}
constexpr _ALWAYS_INLINE_ float remap(float p_value, float p_istart, float p_istop, float p_ostart, float p_ostop) {
	return lerp(p_ostart, p_ostop, inverse_lerp(p_istart, p_istop, p_value));
}

constexpr _ALWAYS_INLINE_ bool is_equal_approx(double p_left, double p_right, double p_tolerance) {
	// Check for exact equality first, required to handle "infinity" values.
	if (p_left == p_right) {
		return true;
	}
	// Then check for approximate equality.
	return abs(p_left - p_right) < p_tolerance;
}
constexpr _ALWAYS_INLINE_ bool is_equal_approx(float p_left, float p_right, float p_tolerance) {
	// Check for exact equality first, required to handle "infinity" values.
	if (p_left == p_right) {
		return true;
	}
	// Then check for approximate equality.
	return abs(p_left - p_right) < p_tolerance;
}

constexpr _ALWAYS_INLINE_ bool is_equal_approx(double p_left, double p_right) {
	// Check for exact equality first, required to handle "infinity" values.
	if (p_left == p_right) {
		return true;
	}
	// Then check for approximate equality.
	double tolerance = CMP_EPSILON * abs(p_left);
	if (tolerance < CMP_EPSILON) {
		tolerance = CMP_EPSILON;
	}
	return abs(p_left - p_right) < tolerance;
}
constexpr _ALWAYS_INLINE_ bool is_equal_approx(float p_left, float p_right) {
	// Check for exact equality first, required to handle "infinity" values.
	if (p_left == p_right) {
		return true;
	}
	// Then check for approximate equality.
	float tolerance = (float)CMP_EPSILON * abs(p_left);
	if (tolerance < (float)CMP_EPSILON) {
		tolerance = (float)CMP_EPSILON;
	}
	return abs(p_left - p_right) < tolerance;
}

constexpr _ALWAYS_INLINE_ bool is_zero_approx(double p_value) {
	return abs(p_value) < CMP_EPSILON;
}
constexpr _ALWAYS_INLINE_ bool is_zero_approx(float p_value) {
	return abs(p_value) < F_CMP_EPSILON;
}

constexpr _ALWAYS_INLINE_ bool is_same(double p_left, double p_right) {
	return (p_left == p_right) || (is_nan(p_left) && is_nan(p_right));
}
constexpr _ALWAYS_INLINE_ bool is_same(float p_left, float p_right) {
	return (p_left == p_right) || (is_nan(p_left) && is_nan(p_right));
}

constexpr _ALWAYS_INLINE_ double smoothstep(double p_from, double p_to, double p_s) {
	if (is_equal_approx(p_from, p_to)) {
		if (likely(p_from <= p_to)) {
			return p_s <= p_from ? 0.0 : 1.0;
		} else {
			return p_s <= p_to ? 1.0 : 0.0;
		}
	}
	double s = CLAMP((p_s - p_from) / (p_to - p_from), 0.0, 1.0);
	return s * s * (3.0 - 2.0 * s);
}
constexpr _ALWAYS_INLINE_ float smoothstep(float p_from, float p_to, float p_s) {
	if (is_equal_approx(p_from, p_to)) {
		if (likely(p_from <= p_to)) {
			return p_s <= p_from ? 0.0f : 1.0f;
		} else {
			return p_s <= p_to ? 1.0f : 0.0f;
		}
	}
	float s = CLAMP((p_s - p_from) / (p_to - p_from), 0.0f, 1.0f);
	return s * s * (3.0f - 2.0f * s);
}

constexpr _ALWAYS_INLINE_ double move_toward(double p_from, double p_to, double p_delta) {
	return abs(p_to - p_from) <= p_delta ? p_to : p_from + SIGN(p_to - p_from) * p_delta;
}
constexpr _ALWAYS_INLINE_ float move_toward(float p_from, float p_to, float p_delta) {
	return abs(p_to - p_from) <= p_delta ? p_to : p_from + SIGN(p_to - p_from) * p_delta;
}

constexpr _ALWAYS_INLINE_ double rotate_toward(double p_from, double p_to, double p_delta) {
	double difference = angle_difference(p_from, p_to);
	double abs_difference = abs(difference);
	// When `p_delta < 0` move no further than to PI radians away from `p_to` (as PI is the max possible angle distance).
	return p_from + CLAMP(p_delta, abs_difference - PI, abs_difference) * (difference >= 0.0 ? 1.0 : -1.0);
}
constexpr _ALWAYS_INLINE_ float rotate_toward(float p_from, float p_to, float p_delta) {
	float difference = angle_difference(p_from, p_to);
	float abs_difference = abs(difference);
	// When `p_delta < 0` move no further than to PI radians away from `p_to` (as PI is the max possible angle distance).
	return p_from + CLAMP(p_delta, abs_difference - F_PI, abs_difference) * (difference >= 0.0f ? 1.0f : -1.0f);
}

constexpr _ALWAYS_INLINE_ double linear_to_db(double p_linear) {
	return log(p_linear) * 8.6858896380650365530225783783321;
}
constexpr _ALWAYS_INLINE_ float linear_to_db(float p_linear) {
	return log(p_linear) * 8.6858896380650365530225783783321F;
}

constexpr _ALWAYS_INLINE_ double db_to_linear(double p_db) {
	return exp(p_db * 0.11512925464970228420089957273422);
}
constexpr _ALWAYS_INLINE_ float db_to_linear(float p_db) {
	return exp(p_db * 0.11512925464970228420089957273422f);
}

_ALWAYS_INLINE_ double round(double p_val) {
	return std::round(p_val);
}
_ALWAYS_INLINE_ float round(float p_val) {
	return std::round(p_val);
}

_ALWAYS_INLINE_ double wrapf(double p_value, double p_min, double p_max) {
	double range = p_max - p_min;
	if (is_zero_approx(range)) {
		return p_min;
	}
	double result = p_value - (range * floor((p_value - p_min) / range));
	if (is_equal_approx(result, p_max)) {
		return p_min;
	}
	return result;
}
_ALWAYS_INLINE_ float wrapf(float p_value, float p_min, float p_max) {
	float range = p_max - p_min;
	if (is_zero_approx(range)) {
		return p_min;
	}
	float result = p_value - (range * floor((p_value - p_min) / range));
	if (is_equal_approx(result, p_max)) {
		return p_min;
	}
	return result;
}

_ALWAYS_INLINE_ int64_t wrapi(int64_t p_value, int64_t p_min, int64_t p_max) {
	int64_t range = p_max - p_min;
	return range == 0 ? p_min : p_min + ((((p_value - p_min) % range) + range) % range);
}

_ALWAYS_INLINE_ double fract(double p_value) {
	return p_value - floor(p_value);
}
_ALWAYS_INLINE_ float fract(float p_value) {
	return p_value - floor(p_value);
}

_ALWAYS_INLINE_ double pingpong(double p_value, double p_length) {
	return (p_length != 0.0) ? abs(fract((p_value - p_length) / (p_length * 2.0)) * p_length * 2.0 - p_length) : 0.0;
}
_ALWAYS_INLINE_ float pingpong(float p_value, float p_length) {
	return (p_length != 0.0f) ? abs(fract((p_value - p_length) / (p_length * 2.0f)) * p_length * 2.0f - p_length) : 0.0f;
}

// double only, as these functions are mainly used by the editor and not performance-critical,
double ease(double p_x, double p_c);
int step_decimals(double p_step);
int range_step_decimals(double p_step); // For editor use only.
double snapped(double p_value, double p_step);

uint32_t larger_prime(uint32_t p_val);

void seed(uint64_t p_seed);
void randomize();
uint32_t rand_from_seed(uint64_t *p_seed);
uint32_t rand();
_ALWAYS_INLINE_ double randd() {
	return (double)rand() / (double)UINT32_MAX;
}
_ALWAYS_INLINE_ float randf() {
	return (float)rand() / (float)UINT32_MAX;
}
double randfn(double p_mean, double p_deviation);

double random(double p_from, double p_to);
float random(float p_from, float p_to);
int random(int p_from, int p_to);

// This function should be as fast as possible and rounding mode should not matter.
_ALWAYS_INLINE_ int fast_ftoi(float p_value) {
	return std::rint(p_value);
}

_ALWAYS_INLINE_ uint32_t halfbits_to_floatbits(uint16_t p_half) {
	uint16_t h_exp, h_sig;
	uint32_t f_sgn, f_exp, f_sig;

	h_exp = (p_half & 0x7c00u);
	f_sgn = ((uint32_t)p_half & 0x8000u) << 16;
	switch (h_exp) {
		case 0x0000u: /* 0 or subnormal */
			h_sig = (p_half & 0x03ffu);
			/* Signed zero */
			if (h_sig == 0) {
				return f_sgn;
			}
			/* Subnormal */
			h_sig <<= 1;
			while ((h_sig & 0x0400u) == 0) {
				h_sig <<= 1;
				h_exp++;
			}
			f_exp = ((uint32_t)(127 - 15 - h_exp)) << 23;
			f_sig = ((uint32_t)(h_sig & 0x03ffu)) << 13;
			return f_sgn + f_exp + f_sig;
		case 0x7c00u: /* inf or NaN */
			/* All-ones exponent and a copy of the significand */
			return f_sgn + 0x7f800000u + (((uint32_t)(p_half & 0x03ffu)) << 13);
		default: /* normalized */
			/* Just need to adjust the exponent and shift */
			return f_sgn + (((uint32_t)(p_half & 0x7fffu) + 0x1c000u) << 13);
	}
}

_ALWAYS_INLINE_ float halfptr_to_float(const uint16_t *p_half) {
	union {
		uint32_t u32;
		float f32;
	} u;

	u.u32 = halfbits_to_floatbits(*p_half);
	return u.f32;
}

_ALWAYS_INLINE_ float half_to_float(const uint16_t p_half) {
	return halfptr_to_float(&p_half);
}

_ALWAYS_INLINE_ uint16_t make_half_float(float p_value) {
	union {
		float fv;
		uint32_t ui;
	} ci;
	ci.fv = p_value;

	uint32_t x = ci.ui;
	uint32_t sign = (unsigned short)(x >> 31);
	uint32_t mantissa;
	uint32_t exponent;
	uint16_t hf;

	// get mantissa
	mantissa = x & ((1 << 23) - 1);
	// get exponent bits
	exponent = x & (0xFF << 23);
	if (exponent >= 0x47800000) {
		// check if the original single precision float number is a NaN
		if (mantissa && (exponent == (0xFF << 23))) {
			// we have a single precision NaN
			mantissa = (1 << 23) - 1;
		} else {
			// 16-bit half-float representation stores number as Inf
			mantissa = 0;
		}
		hf = (((uint16_t)sign) << 15) | (uint16_t)((0x1F << 10)) |
				(uint16_t)(mantissa >> 13);
	}
	// check if exponent is <= -15
	else if (exponent <= 0x38000000) {
		/*
		// store a denorm half-float value or zero
		exponent = (0x38000000 - exponent) >> 23;
		mantissa >>= (14 + exponent);

		hf = (((uint16_t)sign) << 15) | (uint16_t)(mantissa);
		*/
		hf = 0; //denormals do not work for 3D, convert to zero
	} else {
		hf = (((uint16_t)sign) << 15) |
				(uint16_t)((exponent - 0x38000000) >> 13) |
				(uint16_t)(mantissa >> 13);
	}

	return hf;
}

_ALWAYS_INLINE_ float snap_scalar(float p_offset, float p_step, float p_target) {
	return p_step != 0 ? snapped(p_target - p_offset, p_step) + p_offset : p_target;
}

_ALWAYS_INLINE_ float snap_scalar_separation(float p_offset, float p_step, float p_target, float p_separation) {
	if (p_step != 0) {
		float a = snapped(p_target - p_offset, p_step + p_separation) + p_offset;
		float b = a;
		if (p_target >= 0) {
			b -= p_separation;
		} else {
			b += p_step;
		}
		return (abs(p_target - a) < abs(p_target - b)) ? a : b;
	}
	return p_target;
}

}; // namespace Math
