name: 🍎 macOS Builds
on:
  workflow_call:

# Global Settings
env:
  SCONS_FLAGS: >-
    dev_mode=yes
    module_text_server_fb_enabled=yes
    "accesskit_sdk_path=${{ github.workspace }}/accesskit-c-0.17.0/"

jobs:
  build-macos:
    runs-on: macos-latest
    name: ${{ matrix.name }}
    timeout-minutes: 120
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Editor (target=editor)
            cache-name: macos-editor
            target: editor
            bin: ./bin/godot.macos.editor.universal

          - name: Template (target=template_release)
            cache-name: macos-template
            target: template_release
            scons-flags: debug_symbols=no
            bin: ./bin/godot.macos.template_release.universal

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Select Xcode 16
        run: sudo xcode-select -s /Applications/Xcode_16.2.app

      - name: Restore Godot build cache
        uses: ./.github/actions/godot-cache-restore
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Setup Python and SCons
        uses: ./.github/actions/godot-deps

      - name: Download pre-built AccessKit
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: AccessKit/accesskit-c
          version: tags/0.17.0
          file: accesskit-c-0.17.0.zip
          target: accesskit-c-0.17.0/accesskit_c.zip

      - name: Extract pre-built AccessKit
        run: unzip -o accesskit-c-0.17.0/accesskit_c.zip

      - name: Setup Vulkan SDK
        id: vulkan-sdk
        run: |
          if sh misc/scripts/install_vulkan_sdk_macos.sh; then
            echo "VULKAN_ENABLED=yes" >> "$GITHUB_OUTPUT"
          else
            echo "::warning::macOS: Vulkan SDK installation failed, building without Vulkan support."
            echo "VULKAN_ENABLED=no" >> "$GITHUB_OUTPUT"
          fi
        continue-on-error: true

      - name: Compilation (x86_64)
        uses: ./.github/actions/godot-build
        with:
          scons-flags: ${{ env.SCONS_FLAGS }} ${{ matrix.scons-flags }} arch=x86_64 vulkan=${{ steps.vulkan-sdk.outputs.VULKAN_ENABLED }}
          platform: macos
          target: ${{ matrix.target }}

      - name: Compilation (arm64)
        uses: ./.github/actions/godot-build
        with:
          scons-flags: ${{ env.SCONS_FLAGS }} ${{ matrix.scons-flags }} arch=arm64 vulkan=${{ steps.vulkan-sdk.outputs.VULKAN_ENABLED }}
          platform: macos
          target: ${{ matrix.target }}

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Prepare artifact
        run: |
          lipo -create ./bin/godot.macos.${{ matrix.target }}.x86_64 ./bin/godot.macos.${{ matrix.target }}.arm64 -output ./bin/godot.macos.${{ matrix.target }}.universal
          rm ./bin/godot.macos.${{ matrix.target }}.x86_64 ./bin/godot.macos.${{ matrix.target }}.arm64
          strip bin/godot.*
          chmod +x bin/godot.*

      - name: Upload artifact
        uses: ./.github/actions/upload-artifact
        with:
          name: ${{ matrix.cache-name }}

      - name: Unit tests
        run: |
          ${{ matrix.bin }} --version
          ${{ matrix.bin }} --help
          ${{ matrix.bin }} --test --force-colors
